'use client'

import { useState, useEffect, useRef } from 'react'
import styles from './video-intro.module.css'

interface VideoIntroProps {
  isVisible: boolean
  onComplete: () => void
}

export default function VideoIntro({ isVisible, onComplete }: VideoIntroProps) {
  const [showIntro, setShowIntro] = useState(false)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const introSoundTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  
  const introImages = [
    'https://embrsreading.com/wp-content/uploads/2025/08/intro.png',
    'https://embrsreading.com/wp-content/uploads/2025/08/123.png',
    'https://embrsreading.com/wp-content/uploads/2025/08/ch.png',
    'https://embrsreading.com/wp-content/uploads/2025/08/i.png',
    'https://embrsreading.com/wp-content/uploads/2025/08/kn.png',
    'https://embrsreading.com/wp-content/uploads/2025/08/m.png',
    'https://embrsreading.com/wp-content/uploads/2025/08/o.png',
    'https://embrsreading.com/wp-content/uploads/2025/08/or.png',
    'https://embrsreading.com/wp-content/uploads/2025/08/ph.png',
    'https://embrsreading.com/wp-content/uploads/2025/08/sh.png',
    'https://embrsreading.com/wp-content/uploads/2025/08/sort.png',
    'https://embrsreading.com/wp-content/uploads/2025/08/testingemb-1.png',
    'https://embrsreading.com/wp-content/uploads/2025/08/ue.png'
  ]

  useEffect(() => {
    if (isVisible) {
      setShowIntro(false)
      setCurrentImageIndex(0)
      startIntro()
    }
  }, [isVisible])

  // Cleanup effect
  useEffect(() => {
    return () => {
      // Clear all timers on unmount
      if (intervalRef.current) clearInterval(intervalRef.current)
      if (timeoutRef.current) clearTimeout(timeoutRef.current)
      if (introSoundTimeoutRef.current) clearTimeout(introSoundTimeoutRef.current)

      // Stop all audio on unmount
      const music = document.getElementById('epic-music') as HTMLAudioElement
      const introSound = document.getElementById('intro-sound') as HTMLAudioElement

      if (music) {
        music.pause()
        music.currentTime = 0
      }
      if (introSound) {
        introSound.pause()
        introSound.currentTime = 0
      }
    }
  }, [])

  const startIntro = () => {
    setShowIntro(true)

    // Clear any existing timers
    if (intervalRef.current) clearInterval(intervalRef.current)
    if (timeoutRef.current) clearTimeout(timeoutRef.current)
    if (introSoundTimeoutRef.current) clearTimeout(introSoundTimeoutRef.current)

    const music = document.getElementById('epic-music') as HTMLAudioElement
    const introSound = document.getElementById('intro-sound') as HTMLAudioElement

    if (music) {
      music.currentTime = 0
      music.volume = 0.3
      music.play().catch(e => console.error('Music failed to play:', e))
    }

    introSoundTimeoutRef.current = setTimeout(() => {
      const introSound = document.getElementById('intro-sound') as HTMLAudioElement
      if (introSound) {
        introSound.currentTime = 0
        introSound.play().catch(e => console.error('Sound effect failed to play:', e))
      }
    }, 2200)

    intervalRef.current = setInterval(() => {
      setCurrentImageIndex(prev => (prev + 1) % introImages.length)
    }, 4000)

    timeoutRef.current = setTimeout(() => {
      endIntro()
    }, 30000)
  }
  
  const endIntro = () => {
    setShowIntro(false)

    // Clear all timers
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
    if (introSoundTimeoutRef.current) {
      clearTimeout(introSoundTimeoutRef.current)
      introSoundTimeoutRef.current = null
    }

    // Stop all audio
    const music = document.getElementById('epic-music') as HTMLAudioElement
    const introSound = document.getElementById('intro-sound') as HTMLAudioElement

    if (music) {
      music.pause()
      music.currentTime = 0
    }
    if (introSound) {
      introSound.pause()
      introSound.currentTime = 0
    }

    setTimeout(() => {
      onComplete()
    }, 1000)
  }

  if (!isVisible) return null
  
  return (
    <>
      <div id='intro-sparkle-container' className={styles.introSparkleContainer}></div>
      
      {showIntro && (
        <>
          <div className={styles.cinematicIntro}>
            {introImages.map((src, index) => (
              <img
                key={index}
                src={src}
                className={`${styles.introImage} ${index === currentImageIndex ? styles.active : ''}`}
                alt='Cinematic intro image'
              />
            ))}
          </div>
          
          <button className={styles.skipIntroBtn} onClick={endIntro}>
            Skip
          </button>
        </>
      )}
      
      <audio id='epic-music' src='https://embrsreading.com/wp-content/uploads/2025/08/embrsmusic.mp3' preload='auto'></audio>
      <audio id='intro-sound' src='https://embrsreading.com/wp-content/uploads/2025/08/openai-fm-coral-audio-1.wav' preload='auto'></audio>
    </>
  )
}