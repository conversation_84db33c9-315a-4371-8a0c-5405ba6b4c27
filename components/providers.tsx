"use client"

import { ReactNode } from 'react'
import { SessionProvider } from 'next-auth/react'
import { PresenterProvider } from '@/components/presenter-context'
import { LessonDataProvider } from '@/components/lesson-data-context'
import { SoundManagerProvider } from '@/contexts/sound-manager-context'

interface ProvidersProps {
  children: ReactNode
}

export default function Providers({ children }: ProvidersProps) {
  return (
    <SessionProvider>
      <SoundManagerProvider>
        <PresenterProvider>
          <LessonDataProvider>
            {children}
          </LessonDataProvider>
        </PresenterProvider>
      </SoundManagerProvider>
    </SessionProvider>
  )
}
